"""Pytest configuration and fixtures for LLASA TTS API tests."""

import pytest
import asyncio
from fastapi.testclient import <PERSON><PERSON><PERSON>
from unittest.mock import Mock, patch
import numpy as np

from api.app import app
from api.models.llasa import llasa_model
from api.utils.audio import audio_processor


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def client():
    """Create a test client for the FastAPI app."""
    with TestClient(app) as test_client:
        yield test_client


@pytest.fixture
def mock_llasa_model():
    """Mock LLASA model for testing."""
    with patch('api.models.llasa.llasa_model') as mock_model:
        mock_model.is_loaded = True
        mock_model.text_to_speech.return_value = np.random.randn(16000)  # 1 second of audio
        mock_model.voice_clone_tts.return_value = np.random.randn(32000)  # 2 seconds of audio
        yield mock_model


@pytest.fixture
def mock_audio_processor():
    """Mock audio processor for testing."""
    with patch('api.utils.audio.audio_processor') as mock_processor:
        mock_processor.validate_audio_file.return_value = True
        mock_processor.load_audio_from_bytes.return_value = (np.random.randn(16000), 16000)
        mock_processor.save_audio_to_bytes.return_value = b"fake_audio_data"
        mock_processor.normalize_audio.return_value = np.random.randn(16000)
        yield mock_processor


@pytest.fixture
def sample_audio_bytes():
    """Generate sample audio bytes for testing."""
    # Create a simple WAV file header + data
    import struct
    import io
    
    # WAV header for 16kHz, 16-bit, mono, 1 second
    sample_rate = 16000
    duration = 1.0
    num_samples = int(sample_rate * duration)
    
    # Generate sine wave
    audio_data = np.sin(2 * np.pi * 440 * np.linspace(0, duration, num_samples))
    audio_data = (audio_data * 32767).astype(np.int16)
    
    # Create WAV file in memory
    wav_io = io.BytesIO()
    
    # WAV header
    wav_io.write(b'RIFF')
    wav_io.write(struct.pack('<I', 36 + len(audio_data) * 2))
    wav_io.write(b'WAVE')
    wav_io.write(b'fmt ')
    wav_io.write(struct.pack('<I', 16))  # PCM format chunk size
    wav_io.write(struct.pack('<H', 1))   # PCM format
    wav_io.write(struct.pack('<H', 1))   # Mono
    wav_io.write(struct.pack('<I', sample_rate))
    wav_io.write(struct.pack('<I', sample_rate * 2))  # Byte rate
    wav_io.write(struct.pack('<H', 2))   # Block align
    wav_io.write(struct.pack('<H', 16))  # Bits per sample
    wav_io.write(b'data')
    wav_io.write(struct.pack('<I', len(audio_data) * 2))
    wav_io.write(audio_data.tobytes())
    
    return wav_io.getvalue()


@pytest.fixture
def sample_text():
    """Sample text for testing."""
    return "This is a test sentence for TTS generation."


@pytest.fixture
def long_text():
    """Long text that exceeds limits."""
    return "A" * 1001  # Exceeds max_text_length of 1000


@pytest.fixture
def empty_text():
    """Empty text for testing validation."""
    return ""


@pytest.fixture
def sample_prompt_text():
    """Sample prompt text for voice cloning."""
    return "This is the original voice sample."
