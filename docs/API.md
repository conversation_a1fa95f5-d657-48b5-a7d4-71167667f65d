# LLASA TTS API Documentation

## Overview

The LLASA TTS API provides REST endpoints for text-to-speech synthesis and voice cloning using the LLASA-3B model from HKUST-Audio.

## Base URL

```
http://localhost:8000/api/v1
```

## Authentication

Currently, no authentication is required. For production use, implement appropriate authentication mechanisms.

## Endpoints

### Health Check

#### GET /health

Check the API status and model loading state.

**Response:**
```json
{
  "status": "healthy",
  "models_loaded": true,
  "device": "cuda"
}
```

### Text-to-Speech

#### POST /tts

Convert text to speech without voice cloning.

**Request Body:**
```json
{
  "text": "Hello, this is a test of the LLASA text-to-speech system."
}
```

**Response:**
- Content-Type: `audio/wav`
- Headers:
  - `Content-Disposition`: `attachment; filename=tts_output.wav`
  - `X-Audio-Duration`: Duration in seconds

**Example using curl:**
```bash
curl -X POST "http://localhost:8000/api/v1/tts" \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello world!"}' \
  --output output.wav
```

#### POST /tts/simple

Simple TTS endpoint that returns JSON response instead of audio file.

**Request Body:**
```json
{
  "text": "Hello, this is a test of the LLASA text-to-speech system."
}
```

**Response:**
```json
{
  "success": true,
  "message": "Audio generated successfully",
  "audio_duration": 3.5
}
```

### Voice Cloning

#### POST /tts/voice-clone

Convert text to speech with voice cloning using an audio prompt.

**Request (multipart/form-data):**
- `text` (string): Text to convert to speech
- `prompt_text` (string): Text corresponding to the audio prompt
- `audio_file` (file): Audio file for voice cloning

**Response:**
- Content-Type: `audio/wav`
- Headers:
  - `Content-Disposition`: `attachment; filename=voice_clone_output.wav`
  - `X-Audio-Duration`: Duration in seconds

**Example using curl:**
```bash
curl -X POST "http://localhost:8000/api/v1/tts/voice-clone" \
  -F "text=This is the text I want to synthesize with the cloned voice." \
  -F "prompt_text=This is the text that corresponds to the uploaded audio file." \
  -F "audio_file=@prompt_audio.wav" \
  --output cloned_output.wav
```

## Error Responses

All endpoints may return error responses in the following format:

```json
{
  "success": false,
  "error": "Error message",
  "detail": "Detailed error information (optional)"
}
```

### Common Error Codes

- `400 Bad Request`: Invalid input data
- `413 Payload Too Large`: File or text too large
- `422 Unprocessable Entity`: Validation error
- `500 Internal Server Error`: Server error
- `503 Service Unavailable`: Models not loaded

## Limits

- Maximum text length: 1000 characters
- Maximum audio file size: 10MB
- Maximum audio duration: 30 seconds
- Supported audio formats: WAV, MP3, FLAC, OGG, M4A
- Output format: WAV (16kHz)

## Interactive Documentation

Visit `http://localhost:8000/docs` for interactive Swagger UI documentation.
Visit `http://localhost:8000/redoc` for ReDoc documentation.
