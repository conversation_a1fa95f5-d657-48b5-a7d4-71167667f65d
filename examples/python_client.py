"""Python client examples for LLASA TTS API."""

import requests
import json
from pathlib import Path


class LLASAClient:
    """Python client for LLASA TTS API."""
    
    def __init__(self, base_url: str = "http://localhost:8000/api/v1"):
        self.base_url = base_url
    
    def health_check(self) -> dict:
        """Check API health."""
        response = requests.get(f"{self.base_url}/health")
        response.raise_for_status()
        return response.json()
    
    def text_to_speech(self, text: str, output_file: str = "output.wav") -> bool:
        """Convert text to speech."""
        data = {"text": text}
        
        response = requests.post(
            f"{self.base_url}/tts",
            json=data,
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        
        # Save audio file
        with open(output_file, "wb") as f:
            f.write(response.content)
        
        # Get duration from headers
        duration = response.headers.get("X-Audio-Duration")
        print(f"Audio saved to {output_file} (duration: {duration}s)")
        
        return True
    
    def voice_clone_tts(
        self, 
        text: str, 
        prompt_text: str, 
        audio_file_path: str, 
        output_file: str = "cloned_output.wav"
    ) -> bool:
        """Convert text to speech with voice cloning."""
        
        # Prepare form data
        files = {
            'audio_file': open(audio_file_path, 'rb')
        }
        data = {
            'text': text,
            'prompt_text': prompt_text
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/tts/voice-clone",
                files=files,
                data=data
            )
            response.raise_for_status()
            
            # Save audio file
            with open(output_file, "wb") as f:
                f.write(response.content)
            
            # Get duration from headers
            duration = response.headers.get("X-Audio-Duration")
            print(f"Cloned audio saved to {output_file} (duration: {duration}s)")
            
            return True
            
        finally:
            files['audio_file'].close()
    
    def simple_tts(self, text: str) -> dict:
        """Simple TTS that returns JSON response."""
        data = {"text": text}
        
        response = requests.post(
            f"{self.base_url}/tts/simple",
            json=data,
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        return response.json()


def main():
    """Example usage of LLASA TTS API."""
    
    # Initialize client
    client = LLASAClient()
    
    # Check health
    try:
        health = client.health_check()
        print(f"API Status: {health}")
        
        if not health.get("models_loaded"):
            print("Models not loaded. Please wait for models to load.")
            return
            
    except requests.exceptions.RequestException as e:
        print(f"API not available: {e}")
        return
    
    # Example 1: Simple TTS
    print("\n=== Example 1: Simple TTS ===")
    try:
        result = client.simple_tts("Hello, this is a test of the LLASA text-to-speech system.")
        print(f"TTS Result: {result}")
    except Exception as e:
        print(f"TTS failed: {e}")
    
    # Example 2: TTS with audio output
    print("\n=== Example 2: TTS with audio output ===")
    try:
        client.text_to_speech(
            "Welcome to the LLASA TTS API. This is an example of text-to-speech synthesis.",
            "example_output.wav"
        )
    except Exception as e:
        print(f"TTS failed: {e}")
    
    # Example 3: Voice cloning (requires audio file)
    print("\n=== Example 3: Voice cloning ===")
    prompt_audio_path = "prompt_audio.wav"  # You need to provide this file
    
    if Path(prompt_audio_path).exists():
        try:
            client.voice_clone_tts(
                text="This is a demonstration of voice cloning using the LLASA model.",
                prompt_text="This is the original voice sample.",
                audio_file_path=prompt_audio_path,
                output_file="voice_cloned_output.wav"
            )
        except Exception as e:
            print(f"Voice cloning failed: {e}")
    else:
        print(f"Prompt audio file not found: {prompt_audio_path}")
        print("Please provide an audio file for voice cloning example.")


if __name__ == "__main__":
    main()
