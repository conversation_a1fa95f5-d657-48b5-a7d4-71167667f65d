#!/bin/bash

# LLASA TTS API - cURL Examples
# Make sure the API is running on localhost:8000

API_BASE="http://localhost:8000/api/v1"

echo "=== LLASA TTS API Examples ==="
echo

# 1. Health Check
echo "1. Health Check:"
curl -X GET "$API_BASE/health" \
  -H "Content-Type: application/json" | jq .
echo
echo

# 2. Simple TTS (JSON response)
echo "2. Simple TTS (JSON response):"
curl -X POST "$API_BASE/tts/simple" \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello, this is a test of the LLASA text-to-speech system."}' | jq .
echo
echo

# 3. TTS with audio output
echo "3. TTS with audio output:"
curl -X POST "$API_BASE/tts" \
  -H "Content-Type: application/json" \
  -d '{"text": "Welcome to the LLASA TTS API. This is an example of text-to-speech synthesis."}' \
  --output tts_output.wav

if [ -f "tts_output.wav" ]; then
    echo "✓ Audio saved to tts_output.wav"
    ls -lh tts_output.wav
else
    echo "✗ Failed to save audio file"
fi
echo
echo

# 4. Voice cloning (requires audio file)
echo "4. Voice cloning TTS:"
if [ -f "prompt_audio.wav" ]; then
    curl -X POST "$API_BASE/tts/voice-clone" \
      -F "text=This is a demonstration of voice cloning using the LLASA model." \
      -F "prompt_text=This is the original voice sample." \
      -F "audio_file=@prompt_audio.wav" \
      --output voice_cloned_output.wav
    
    if [ -f "voice_cloned_output.wav" ]; then
        echo "✓ Cloned audio saved to voice_cloned_output.wav"
        ls -lh voice_cloned_output.wav
    else
        echo "✗ Failed to save cloned audio file"
    fi
else
    echo "⚠ prompt_audio.wav not found. Skipping voice cloning example."
    echo "  Please provide an audio file named 'prompt_audio.wav' to test voice cloning."
fi
echo
echo

# 5. Error handling example
echo "5. Error handling (empty text):"
curl -X POST "$API_BASE/tts" \
  -H "Content-Type: application/json" \
  -d '{"text": ""}' | jq .
echo
echo

# 6. Error handling example (text too long)
echo "6. Error handling (text too long):"
LONG_TEXT=$(python3 -c "print('A' * 1001)")
curl -X POST "$API_BASE/tts/simple" \
  -H "Content-Type: application/json" \
  -d "{\"text\": \"$LONG_TEXT\"}" | jq .
echo
echo

echo "=== Examples completed ==="
