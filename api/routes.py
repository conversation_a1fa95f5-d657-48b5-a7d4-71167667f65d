"""FastAPI routes for LLASA TTS API."""

import io
from fastapi import APIRouter, File, UploadFile, Form, HTTPException, Response
from fastapi.responses import StreamingResponse
from loguru import logger
from typing import Optional

from .schemas import TTSRequest, VoiceCloneRequest, TTSResponse, HealthResponse, ErrorResponse
from .models.llasa import llasa_model
from .utils.audio import audio_processor
from .config import settings


router = APIRouter()


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Check API health and model status."""
    return HealthResponse(
        status="healthy",
        models_loaded=llasa_model.is_loaded,
        device=settings.device
    )


@router.post("/tts", response_model=TTSResponse)
async def text_to_speech(request: TTSRequest):
    """Convert text to speech without voice cloning."""
    try:
        if not llasa_model.is_loaded:
            raise HTTPException(status_code=503, detail="Models not loaded")
        
        # Generate audio
        audio_array = llasa_model.text_to_speech(request.text)
        
        # Calculate duration
        duration = len(audio_array) / settings.sample_rate
        
        # Convert to bytes
        audio_bytes = audio_processor.save_audio_to_bytes(audio_array, settings.audio_format)
        
        # Create response
        response = StreamingResponse(
            io.BytesIO(audio_bytes),
            media_type=f"audio/{settings.audio_format}",
            headers={
                "Content-Disposition": f"attachment; filename=tts_output.{settings.audio_format}",
                "X-Audio-Duration": str(duration)
            }
        )
        
        return response
        
    except ValueError as e:
        logger.error(f"Validation error in TTS: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error in TTS: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/tts/voice-clone", response_model=TTSResponse)
async def voice_clone_tts(
    text: str = Form(..., description="Text to convert to speech"),
    prompt_text: str = Form(..., description="Text corresponding to the audio prompt"),
    audio_file: UploadFile = File(..., description="Audio file for voice cloning")
):
    """Convert text to speech with voice cloning using an audio prompt."""
    try:
        if not llasa_model.is_loaded:
            raise HTTPException(status_code=503, detail="Models not loaded")
        
        # Validate input lengths
        if len(text) > settings.max_text_length:
            raise HTTPException(
                status_code=400, 
                detail=f"Text too long. Maximum length: {settings.max_text_length} characters"
            )
        
        if len(prompt_text) > 500:  # Reasonable limit for prompt text
            raise HTTPException(
                status_code=400, 
                detail="Prompt text too long. Maximum length: 500 characters"
            )
        
        # Read and validate audio file
        audio_data = await audio_file.read()
        audio_processor.validate_audio_file(audio_data)
        
        # Load audio
        prompt_audio, _ = audio_processor.load_audio_from_bytes(audio_data)
        
        # Generate audio with voice cloning
        audio_array = llasa_model.voice_clone_tts(text, prompt_audio, prompt_text)
        
        # Calculate duration
        duration = len(audio_array) / settings.sample_rate
        
        # Convert to bytes
        audio_bytes = audio_processor.save_audio_to_bytes(audio_array, settings.audio_format)
        
        # Create response
        response = StreamingResponse(
            io.BytesIO(audio_bytes),
            media_type=f"audio/{settings.audio_format}",
            headers={
                "Content-Disposition": f"attachment; filename=voice_clone_output.{settings.audio_format}",
                "X-Audio-Duration": str(duration)
            }
        )
        
        return response
        
    except ValueError as e:
        logger.error(f"Validation error in voice clone TTS: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error in voice clone TTS: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/tts/simple", response_model=TTSResponse)
async def simple_tts(request: TTSRequest):
    """Simple TTS endpoint that returns JSON response with success status."""
    try:
        if not llasa_model.is_loaded:
            raise HTTPException(status_code=503, detail="Models not loaded")
        
        # Generate audio
        audio_array = llasa_model.text_to_speech(request.text)
        
        # Calculate duration
        duration = len(audio_array) / settings.sample_rate
        
        return TTSResponse(
            success=True,
            message="Audio generated successfully",
            audio_duration=duration
        )
        
    except ValueError as e:
        logger.error(f"Validation error in simple TTS: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error in simple TTS: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
