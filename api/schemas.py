"""Pydantic schemas for API request/response models."""

from pydantic import BaseModel, Field
from typing import Optional


class TTSRequest(BaseModel):
    """Request model for text-to-speech."""
    text: str = Field(..., description="Text to convert to speech", max_length=1000)
    
    class Config:
        schema_extra = {
            "example": {
                "text": "Hello, this is a test of the LLASA text-to-speech system."
            }
        }


class VoiceCloneRequest(BaseModel):
    """Request model for voice cloning TTS."""
    text: str = Field(..., description="Text to convert to speech", max_length=1000)
    prompt_text: str = Field(..., description="Text corresponding to the audio prompt", max_length=500)
    
    class Config:
        schema_extra = {
            "example": {
                "text": "This is the text I want to synthesize with the cloned voice.",
                "prompt_text": "This is the text that corresponds to the uploaded audio file."
            }
        }


class TTSResponse(BaseModel):
    """Response model for TTS operations."""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Status message")
    audio_duration: Optional[float] = Field(None, description="Duration of generated audio in seconds")
    
    class Config:
        schema_extra = {
            "example": {
                "success": True,
                "message": "Audio generated successfully",
                "audio_duration": 3.5
            }
        }


class HealthResponse(BaseModel):
    """Response model for health check."""
    status: str = Field(..., description="API status")
    models_loaded: bool = Field(..., description="Whether models are loaded")
    device: str = Field(..., description="Device being used for inference")
    
    class Config:
        schema_extra = {
            "example": {
                "status": "healthy",
                "models_loaded": True,
                "device": "cuda"
            }
        }


class ErrorResponse(BaseModel):
    """Response model for errors."""
    success: bool = Field(False, description="Always false for errors")
    error: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Detailed error information")
    
    class Config:
        schema_extra = {
            "example": {
                "success": False,
                "error": "Invalid input",
                "detail": "Text length exceeds maximum allowed length"
            }
        }
