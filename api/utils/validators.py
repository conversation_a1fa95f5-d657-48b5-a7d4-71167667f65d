"""Input validation utilities for LLASA TTS API."""

import re
from typing import Optional
from loguru import logger

from ..config import settings
from .exceptions import ValidationException


class InputValidator:
    """Validates input data for TTS operations."""
    
    @staticmethod
    def validate_text(text: str, max_length: Optional[int] = None) -> str:
        """Validate text input."""
        if not text or not text.strip():
            raise ValidationException("Text cannot be empty")
        
        text = text.strip()
        max_len = max_length or settings.max_text_length
        
        if len(text) > max_len:
            raise ValidationException(f"Text too long. Maximum length: {max_len} characters")
        
        # Check for potentially problematic characters
        if re.search(r'[^\w\s\.,!?;:\'"()\-\u4e00-\u9fff]', text):
            logger.warning("Text contains special characters that might affect synthesis")
        
        return text
    
    @staticmethod
    def validate_audio_duration(duration: float) -> bool:
        """Validate audio duration."""
        if duration <= 0:
            raise ValidationException("Audio duration must be positive")
        
        if duration > settings.max_audio_length:
            raise ValidationException(
                f"Audio too long. Maximum duration: {settings.max_audio_length} seconds"
            )
        
        return True
    
    @staticmethod
    def validate_file_size(file_size: int) -> bool:
        """Validate file size."""
        max_size = settings.max_file_size_mb * 1024 * 1024
        
        if file_size > max_size:
            raise ValidationException(
                f"File too large. Maximum size: {settings.max_file_size_mb}MB"
            )
        
        return True
    
    @staticmethod
    def validate_audio_format(filename: str) -> bool:
        """Validate audio file format."""
        allowed_formats = ['wav', 'mp3', 'flac', 'ogg', 'm4a']
        
        if not filename:
            raise ValidationException("Filename cannot be empty")
        
        extension = filename.lower().split('.')[-1]
        
        if extension not in allowed_formats:
            raise ValidationException(
                f"Unsupported audio format: {extension}. "
                f"Allowed formats: {', '.join(allowed_formats)}"
            )
        
        return True
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """Sanitize filename for safe storage."""
        # Remove path separators and other potentially dangerous characters
        sanitized = re.sub(r'[^\w\-_\.]', '_', filename)
        
        # Limit length
        if len(sanitized) > 255:
            name, ext = sanitized.rsplit('.', 1) if '.' in sanitized else (sanitized, '')
            sanitized = name[:250] + ('.' + ext if ext else '')
        
        return sanitized


# Global validator instance
validator = InputValidator()
