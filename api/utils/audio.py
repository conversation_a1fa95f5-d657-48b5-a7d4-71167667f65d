"""Audio processing utilities for LLASA TTS API."""

import io
import numpy as np
import soundfile as sf
import librosa
import torch
from typing import Tuple, Union, Optional
from pathlib import Path
from loguru import logger

from ..config import settings


class AudioProcessor:
    """Handles audio file processing, validation, and format conversion."""
    
    def __init__(self):
        self.sample_rate = settings.sample_rate
        self.max_file_size_bytes = settings.max_file_size_mb * 1024 * 1024
        self.max_duration = settings.max_audio_length
    
    def validate_audio_file(self, audio_data: bytes) -> bool:
        """Validate audio file size and format."""
        if len(audio_data) > self.max_file_size_bytes:
            raise ValueError(f"Audio file too large. Maximum size: {settings.max_file_size_mb}MB")
        
        try:
            # Try to load the audio to validate format
            audio_array, sr = self.load_audio_from_bytes(audio_data)
            duration = len(audio_array) / sr
            
            if duration > self.max_duration:
                raise ValueError(f"Audio too long. Maximum duration: {self.max_duration} seconds")
            
            return True
        except Exception as e:
            raise ValueError(f"Invalid audio file: {str(e)}")
    
    def load_audio_from_bytes(self, audio_data: bytes) -> Tuple[np.ndarray, int]:
        """Load audio from bytes and return numpy array and sample rate."""
        try:
            audio_io = io.BytesIO(audio_data)
            audio_array, sample_rate = sf.read(audio_io)
            
            # Convert to mono if stereo
            if len(audio_array.shape) > 1:
                audio_array = np.mean(audio_array, axis=1)
            
            # Resample if necessary
            if sample_rate != self.sample_rate:
                audio_array = librosa.resample(
                    audio_array, 
                    orig_sr=sample_rate, 
                    target_sr=self.sample_rate
                )
            
            return audio_array, self.sample_rate
            
        except Exception as e:
            logger.error(f"Error loading audio from bytes: {e}")
            raise ValueError(f"Could not load audio file: {str(e)}")
    
    def load_audio_from_file(self, file_path: Union[str, Path]) -> Tuple[np.ndarray, int]:
        """Load audio from file path."""
        try:
            audio_array, sample_rate = sf.read(str(file_path))
            
            # Convert to mono if stereo
            if len(audio_array.shape) > 1:
                audio_array = np.mean(audio_array, axis=1)
            
            # Resample if necessary
            if sample_rate != self.sample_rate:
                audio_array = librosa.resample(
                    audio_array, 
                    orig_sr=sample_rate, 
                    target_sr=self.sample_rate
                )
            
            return audio_array, self.sample_rate
            
        except Exception as e:
            logger.error(f"Error loading audio from file {file_path}: {e}")
            raise ValueError(f"Could not load audio file: {str(e)}")
    
    def numpy_to_torch(self, audio_array: np.ndarray) -> torch.Tensor:
        """Convert numpy array to torch tensor."""
        return torch.from_numpy(audio_array).float().unsqueeze(0)
    
    def torch_to_numpy(self, audio_tensor: torch.Tensor) -> np.ndarray:
        """Convert torch tensor to numpy array."""
        if audio_tensor.dim() > 1:
            audio_tensor = audio_tensor.squeeze()
        return audio_tensor.cpu().numpy()
    
    def save_audio_to_bytes(self, audio_array: np.ndarray, format: str = "wav") -> bytes:
        """Save audio array to bytes in specified format."""
        try:
            audio_io = io.BytesIO()
            sf.write(audio_io, audio_array, self.sample_rate, format=format.upper())
            audio_io.seek(0)
            return audio_io.read()
        except Exception as e:
            logger.error(f"Error saving audio to bytes: {e}")
            raise ValueError(f"Could not save audio: {str(e)}")
    
    def normalize_audio(self, audio_array: np.ndarray) -> np.ndarray:
        """Normalize audio to prevent clipping."""
        max_val = np.max(np.abs(audio_array))
        if max_val > 0:
            return audio_array / max_val * 0.95
        return audio_array
    
    def trim_silence(self, audio_array: np.ndarray, threshold: float = 0.01) -> np.ndarray:
        """Trim silence from beginning and end of audio."""
        try:
            # Find non-silent regions
            non_silent = np.where(np.abs(audio_array) > threshold)[0]
            if len(non_silent) == 0:
                return audio_array
            
            start_idx = non_silent[0]
            end_idx = non_silent[-1] + 1
            
            return audio_array[start_idx:end_idx]
        except Exception as e:
            logger.warning(f"Could not trim silence: {e}")
            return audio_array


# Global audio processor instance
audio_processor = AudioProcessor()
