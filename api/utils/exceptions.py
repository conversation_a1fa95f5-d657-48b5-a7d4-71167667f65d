"""Custom exceptions for LLASA TTS API."""


class LLASAException(Exception):
    """Base exception for LLASA TTS API."""
    pass


class ModelNotLoadedException(LLASAException):
    """Raised when models are not loaded."""
    pass


class AudioProcessingException(LLASAException):
    """Raised when audio processing fails."""
    pass


class TTSGenerationException(LLASAException):
    """Raised when TTS generation fails."""
    pass


class ValidationException(LLASAException):
    """Raised when input validation fails."""
    pass


class ConfigurationException(LLASAException):
    """Raised when configuration is invalid."""
    pass
