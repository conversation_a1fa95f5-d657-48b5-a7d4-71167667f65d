"""Configuration management for LLASA TTS API."""

import os
from typing import Optional
from pydantic import BaseSettings, Field
from pathlib import Path


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Model Configuration
    llasa_model_path: str = Field(default="HKUSTAudio/Llasa-3B", env="LLASA_MODEL_PATH")
    xcodec_model_path: str = Field(default="HKUSTAudio/xcodec2", env="XCODEC_MODEL_PATH")
    model_cache_dir: str = Field(default="./models", env="MODEL_CACHE_DIR")
    device: str = Field(default="cuda", env="DEVICE")
    
    # API Configuration
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    api_workers: int = Field(default=1, env="API_WORKERS")
    max_audio_length: int = Field(default=30, env="MAX_AUDIO_LENGTH")  # seconds
    max_text_length: int = Field(default=1000, env="MAX_TEXT_LENGTH")  # characters
    
    # Logging Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: Optional[str] = Field(default=None, env="LOG_FILE")
    
    # Audio Configuration
    sample_rate: int = Field(default=16000, env="SAMPLE_RATE")
    audio_format: str = Field(default="wav", env="AUDIO_FORMAT")
    max_file_size_mb: int = Field(default=10, env="MAX_FILE_SIZE_MB")
    
    # Generation Parameters
    max_length: int = Field(default=2048, env="MAX_LENGTH")
    temperature: float = Field(default=0.8, env="TEMPERATURE")
    top_p: float = Field(default=1.0, env="TOP_P")
    do_sample: bool = Field(default=True, env="DO_SAMPLE")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Create necessary directories
        Path(self.model_cache_dir).mkdir(parents=True, exist_ok=True)
        if self.log_file:
            Path(self.log_file).parent.mkdir(parents=True, exist_ok=True)


# Global settings instance
settings = Settings()
