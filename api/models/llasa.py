"""LLASA model wrapper for TTS and voice cloning."""

import torch
import numpy as np
from typing import Optional, List, Union
from transformers import AutoTokenizer, AutoModelForCausalLM
from loguru import logger

from ..config import settings
from ..utils.audio import audio_processor


class LLASAModel:
    """Wrapper class for LLASA-3B model with TTS and voice cloning capabilities."""
    
    def __init__(self):
        self.tokenizer = None
        self.model = None
        self.codec_model = None
        self.device = settings.device
        self.is_loaded = False
        
    def load_models(self):
        """Load LLASA and XCodec2 models."""
        try:
            logger.info("Loading LLASA tokenizer and model...")
            
            # Load LLASA model and tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                settings.llasa_model_path,
                cache_dir=settings.model_cache_dir
            )
            
            self.model = AutoModelForCausalLM.from_pretrained(
                settings.llasa_model_path,
                cache_dir=settings.model_cache_dir,
                torch_dtype=torch.bfloat16 if self.device == "cuda" else torch.float32
            )
            
            self.model.eval()
            self.model.to(self.device)
            
            logger.info("Loading XCodec2 model...")
            
            # Import and load XCodec2 model
            try:
                from xcodec2.modeling_xcodec2 import XCodec2Model
                self.codec_model = XCodec2Model.from_pretrained(
                    settings.xcodec_model_path,
                    cache_dir=settings.model_cache_dir
                )
                self.codec_model.eval()
                self.codec_model.to(self.device)
            except ImportError:
                raise ImportError(
                    "XCodec2 not found. Please install it with: "
                    "pip install git+https://github.com/zhenye234/xcodec2.git"
                )
            
            self.is_loaded = True
            logger.info("Models loaded successfully!")
            
        except Exception as e:
            logger.error(f"Error loading models: {e}")
            raise RuntimeError(f"Failed to load models: {str(e)}")
    
    def _ids_to_speech_tokens(self, speech_ids: List[int]) -> List[str]:
        """Convert speech IDs to speech token strings."""
        return [f"<|s_{speech_id}|>" for speech_id in speech_ids]
    
    def _extract_speech_ids(self, speech_tokens_str: List[str]) -> List[int]:
        """Extract speech IDs from speech token strings."""
        speech_ids = []
        for token_str in speech_tokens_str:
            if token_str.startswith('<|s_') and token_str.endswith('|>'):
                try:
                    num_str = token_str[4:-2]
                    num = int(num_str)
                    speech_ids.append(num)
                except ValueError:
                    logger.warning(f"Could not parse speech token: {token_str}")
            else:
                logger.warning(f"Unexpected token: {token_str}")
        return speech_ids
    
    def text_to_speech(self, text: str) -> np.ndarray:
        """Convert text to speech without voice cloning."""
        if not self.is_loaded:
            raise RuntimeError("Models not loaded. Call load_models() first.")
        
        if len(text) > settings.max_text_length:
            raise ValueError(f"Text too long. Maximum length: {settings.max_text_length} characters")
        
        try:
            with torch.no_grad():
                # Format text for LLASA
                formatted_text = f"<|TEXT_UNDERSTANDING_START|>{text}<|TEXT_UNDERSTANDING_END|>"
                
                # Create chat format
                chat = [
                    {"role": "user", "content": "Convert the text to speech:" + formatted_text},
                    {"role": "assistant", "content": "<|SPEECH_GENERATION_START|>"}
                ]
                
                # Tokenize
                input_ids = self.tokenizer.apply_chat_template(
                    chat,
                    tokenize=True,
                    return_tensors='pt',
                    continue_final_message=True
                )
                input_ids = input_ids.to(self.device)
                
                # Get speech end token ID
                speech_end_id = self.tokenizer.convert_tokens_to_ids('<|SPEECH_GENERATION_END|>')
                
                # Generate speech tokens
                outputs = self.model.generate(
                    input_ids,
                    max_length=settings.max_length,
                    eos_token_id=speech_end_id,
                    do_sample=settings.do_sample,
                    top_p=settings.top_p,
                    temperature=settings.temperature,
                )
                
                # Extract generated speech tokens
                generated_ids = outputs[0][input_ids.shape[1]:-1]
                speech_tokens = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)
                
                # Convert to speech IDs
                speech_ids = self._extract_speech_ids(speech_tokens)
                
                if not speech_ids:
                    raise RuntimeError("No speech tokens generated")
                
                # Convert to tensor and decode
                speech_tokens_tensor = torch.tensor(speech_ids).to(self.device).unsqueeze(0).unsqueeze(0)
                gen_wav = self.codec_model.decode_code(speech_tokens_tensor)
                
                # Convert to numpy
                audio_array = gen_wav[0, 0, :].cpu().numpy()
                
                # Normalize audio
                audio_array = audio_processor.normalize_audio(audio_array)
                
                return audio_array
                
        except Exception as e:
            logger.error(f"Error in text_to_speech: {e}")
            raise RuntimeError(f"TTS generation failed: {str(e)}")
    
    def voice_clone_tts(self, text: str, prompt_audio: np.ndarray, prompt_text: str) -> np.ndarray:
        """Convert text to speech with voice cloning using audio prompt."""
        if not self.is_loaded:
            raise RuntimeError("Models not loaded. Call load_models() first.")
        
        if len(text) > settings.max_text_length:
            raise ValueError(f"Text too long. Maximum length: {settings.max_text_length} characters")
        
        try:
            with torch.no_grad():
                # Convert prompt audio to tensor
                prompt_wav = audio_processor.numpy_to_torch(prompt_audio)
                
                # Encode prompt audio
                vq_code_prompt = self.codec_model.encode_code(input_waveform=prompt_wav)
                vq_code_prompt = vq_code_prompt[0, 0, :]
                
                # Convert to speech tokens
                speech_ids_prefix = self._ids_to_speech_tokens(vq_code_prompt.cpu().numpy())
                
                # Combine prompt text and target text
                combined_text = prompt_text + text
                formatted_text = f"<|TEXT_UNDERSTANDING_START|>{combined_text}<|TEXT_UNDERSTANDING_END|>"
                
                # Create chat format
                chat = [
                    {"role": "user", "content": "Convert the text to speech:" + formatted_text},
                    {"role": "assistant", "content": "<|SPEECH_GENERATION_START|>" + ''.join(speech_ids_prefix)}
                ]
                
                # Tokenize
                input_ids = self.tokenizer.apply_chat_template(
                    chat,
                    tokenize=True,
                    return_tensors='pt',
                    continue_final_message=True
                )
                input_ids = input_ids.to(self.device)
                
                # Get speech end token ID
                speech_end_id = self.tokenizer.convert_tokens_to_ids('<|SPEECH_GENERATION_END|>')
                
                # Generate speech tokens
                outputs = self.model.generate(
                    input_ids,
                    max_length=settings.max_length,
                    eos_token_id=speech_end_id,
                    do_sample=settings.do_sample,
                    top_p=settings.top_p,
                    temperature=settings.temperature,
                )
                
                # Extract generated speech tokens (excluding prefix)
                generated_ids = outputs[0][input_ids.shape[1] - len(speech_ids_prefix):-1]
                speech_tokens = self.tokenizer.batch_decode(generated_ids, skip_special_tokens=True)
                
                # Convert to speech IDs
                speech_ids = self._extract_speech_ids(speech_tokens)
                
                if not speech_ids:
                    raise RuntimeError("No speech tokens generated")
                
                # Convert to tensor and decode
                speech_tokens_tensor = torch.tensor(speech_ids).to(self.device).unsqueeze(0).unsqueeze(0)
                gen_wav = self.codec_model.decode_code(speech_tokens_tensor)
                
                # Convert to numpy
                audio_array = gen_wav[0, 0, :].cpu().numpy()
                
                # Normalize audio
                audio_array = audio_processor.normalize_audio(audio_array)
                
                return audio_array
                
        except Exception as e:
            logger.error(f"Error in voice_clone_tts: {e}")
            raise RuntimeError(f"Voice cloning TTS generation failed: {str(e)}")


# Global model instance
llasa_model = LLASAModel()
